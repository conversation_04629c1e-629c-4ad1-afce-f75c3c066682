const fs = require('fs').promises
const terser = require('terser')
const path = require('path')

/**
 * 查找并用占位符替换 CSS 定义块。
 */
function protectCssBlocks(code) {
  const cssBlocks = []
  const protectedCode = code.replace(/const\s+css\s*=\s*`([\s\S]*?)`;/g, (match) => {
    const placeholder = `/*__CSS_BLOCK_${cssBlocks.length}__*/`
    cssBlocks.push(match)
    return placeholder
  })
  return { protectedCode, cssBlocks }
}

/**
 * 将占位符恢复为原始的 CSS 定义块。
 */
function restoreCssBlocks(code, cssBlocks) {
  let restoredCode = code
  cssBlocks.forEach((block, index) => {
    const placeholderRegex = new RegExp(`\\/\\*__CSS_BLOCK_${index}__\\*\\/\\s*;?`, 'g')
    restoredCode = restoredCode.replace(placeholderRegex, block)
  })
  return restoredCode
}

/**
 * 对 Terser 的输出进行后处理，将指定数组内的多行对象强制格式化为单行。
 */
function postProcessArrayFormatting(code) {
  console.log('  -> Applying surgical single-line object formatting...')
  const arraysToProcess = ['RAW_COMPOUND_WORDS', 'HTML_REPLACEMENT_RULES', 'SETTINGS_CONFIG']
  let processedCode = code

  arraysToProcess.forEach((arrayName) => {
    const regex = new RegExp(`(const\\s+${arrayName}\\s*=\\s*\\[)([\\s\\S]*?)(\\];)`, 'g')
    processedCode = processedCode.replace(regex, (match, start, content, end) => {
      // 1. 将每个多行对象 `{...}` 强制压缩到单行
      const singleLineContent = content.replace(/\{([\s\S]*?)\}/g, (objMatch) => {
        // 移除对象内部所有换行符和多余的空格，然后用单个空格连接
        return (
          '{ ' +
          objMatch
            .replace(/[\r\n]+/g, ' ')
            .replace(/\s{2,}/g, ' ')
            .replace(/\{|\}/g, '')
            .trim() +
          ' }'
        )
      })
      return start + singleLineContent + end
    })
  })
  return processedCode
}

/**
 * 使用最终版流程优化用户脚本。
 */
async function optimizeScript(inputFile, outputFile) {
  try {
    console.log(`Reading file: ${inputFile}`)
    const originalCode = await fs.readFile(inputFile, 'utf8')

    const headerMatch = originalCode.match(/(\/\/ ==UserScript==[\s\S]*?\/\/ ==\/UserScript==)/)
    if (!headerMatch) throw new Error('UserScript header not found.')

    const header = headerMatch[0]
    const body = originalCode.substring(header.length)

    // --- 阶段一：保护 CSS 块 ---
    console.log('Phase 1: Protecting CSS blocks...')
    const { protectedCode, cssBlocks } = protectCssBlocks(body)

    // --- 阶段二：使用 Terser 进行基础清理和美化 ---
    console.log('Phase 2: Basic formatting and comment removal with Terser...')
    const terserResult = await terser.minify(
      { [path.basename(inputFile)]: protectedCode },
      {
        mangle: false,
        compress: false,
        output: { comments: false, beautify: true, indent_level: 2, braces: true },
      }
    )
    if (terserResult.error) throw terserResult.error
    let processedCode = terserResult.code

    // --- 阶段三：对特定数组进行精确的单行对象格式化 ---
    console.log('Phase 3: Applying precise formatting to target arrays...')
    processedCode = postProcessArrayFormatting(processedCode)

    // --- 阶段四：恢复 CSS 块 ---
    console.log('Phase 4: Restoring CSS blocks...')
    processedCode = restoreCssBlocks(processedCode, cssBlocks)

    const finalCode = header + '\n\n' + processedCode

    await fs.writeFile(outputFile, finalCode, 'utf8')
    console.log(`✅ Successfully formatted script and saved to: ${outputFile}`)
  } catch (error) {
    console.error('❌ An error occurred during formatting:', error)
  }
}

// --- 脚本执行逻辑 ---
const args = process.argv.slice(2)
if (args.length >= 2) {
  optimizeScript(args[0], args[1])
} else {
  const defaultInput = 'edewakaru.js'
  const defaultOutput = 'edewakaru.optimized.js'
  console.log(`Usage: node optimizer.js <input_file> <output_file>`)
  console.log(`\nRunning with default files: "${defaultInput}" -> "${defaultOutput}"`)

  fs.access(defaultInput)
    .then(() => optimizeScript(defaultInput, defaultOutput))
    .catch(() => console.error(`Error: Default input file "${defaultInput}" not found.`))
}
