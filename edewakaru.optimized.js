// ==UserScript==
// @name         「絵でわかる日本語」閲覧体験強化
// @namespace    http://tampermonkey.net/
// @version      2025-07-08
// @description  「絵でわかる日本語」サイトの漢字の読み方を括弧書きからルビ表示に自動変換し、広告や不要な要素を非表示にすることで、快適な読書環境を提供します。設定パネルからルビの表示・非表示も簡単に切り替え可能です。
// @icon         https://livedoor.blogimg.jp/edewakaru/imgs/8/c/8cdb7924.png
// <AUTHOR>
// @match        https://www.edewakaru.com/*
// @grant        GM_addStyle
// @grant        GM_getValue
// @grant        GM_setValue
// @run-at       document-start
// ==/UserScript==

(function() {
  ;"use strict";
  const RAW_COMPOUND_WORDS = [ "長い間（ながいあいだ）", "座り心地（すわりごこち）", "触り心地（さわりごこち）", "申し訳（もうしわけ）", "出張（しゅっちょう）", "大好き（だいすき）", "唐揚げ（からあげ）", "立ち読み（たちよみ）", "１杯（いっぱい）", "１回（いっかい）", "１泊（いっぱく）", "１か月（いっかげつ）", "１か月間（いっかげつかん）", "試験（しけん）", "使用（しよう）", "前（まえ）", "待（ま）", "日記（にっき）", "話し手（はなして）", "聞き手（ききて）", "以上（いじょう）", "使い方（つかいかた）", "０点（れいてん）", "買い物（かいもの）", "動作（どうさ）", "m（メートル）", "味覚 （みかく）", "気持ち（きもち）", "青い色（あおいいろ）", "吐き気（はきけ）", "元カレ（もとかれ）", "髪の毛（かみのけ）", "駅（えき）", "万引き（まんびき）", "通（どお）", "遅刻（ちこく）", "経（た）", "三分の一（さんぶんのいち）", "折があれば（おりがあれば）", "折を見て（おりをみて）", "折に触れて（おりにふれて）", "折も折（おりもおり）", "残業（ざんぎょう）", "合（あ）", "楽（たの）", "貸し借り（かしかり）", "入学（にゅうがく）", "暮（ぐ）", "届け出（とどけで）", "有名（ゆうめい）", "自身（じしん）", "住（す）", "夕ご飯（ゆうごはん）", "星の数（ほしのかず）", "窓の外（まどのそと）", "考え方（かんがえかた）", "感じ方（かんじかた）", "貯（た）", "悩み事（なやみごと）", "歩（ある）", "食べず嫌い（たべずぎらい）", "アタック（attack）", "お茶する（おちゃする）", "入（はい）", "使い分け（つかいわけ）", "行き渡る（いきわたる）", "星の数ほどある（ほしのかずほどある）", "星の数ほどいる（ほしのかずほどいる）", "５日間（いつかかん）", "食べ物（たべもの）", "お団子（おだんご）", "足が早い（あしがはやい）", { pattern: "羽根を伸ばす（羽根を伸ばす）", reading: "はねをのばす" }, { pattern: "長蛇の列（長蛇の列）", reading: "ちょうだのれつ" }, { pattern: "付き合（つきあい）", reading: "つきあ" }, { pattern: "目に余る②（めにあまる）", replacement: "<ruby>目<rt>め</rt></ruby>に<ruby>余<rt>あま</rt></ruby>る②" }, { pattern: "言い方（いいかた）", replacement: "<ruby>言<rt>い</rt></ruby>い<ruby>方<rt>かた</rt></ruby>" }, { pattern: "言い訳（いいわけ）", replacement: "<ruby>言<rt>い</rt></ruby>い<ruby>訳<rt>わけ</rt></ruby>" }, { pattern: "目の色が変わる・目の色を変える（めのいろがかわる・かえる）", replacement: "<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>が<ruby>変<rt>かわ</rt></ruby>る・<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>を<ruby>変<rt>かえ</rt></ruby>える" }, { pattern: "水の泡になる・水の泡となる（みずのあわになる）", replacement: "<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>になる・<ruby>水<rt>みず</rt></ruby>の<ruby>泡<rt>あわ</rt></ruby>となる" }, { pattern: "意味で（いみ）", replacement: "<ruby>意味<rt>いみ</rt></ruby>で" }, { pattern: "和製英語で（わせいえいご）", replacement: "<ruby>和製英語<rt>わせいえいご</rt></ruby>で" } ];
  const HTML_REPLACEMENT_RULES = [ { pattern: /一瞬（いっしゅん<br>）/g, replacement: "<ruby>一瞬<rt>いっしゅん</rt></ruby>" }, { pattern: /<b><span style="font-size: 125%;">居<\/span><\/b>（い）/g, replacement: "<b><ruby>居<rt>い</rt></ruby></b>" }, { pattern: /<b style="font-size: large;">留守<\/b>（るす）/g, replacement: "<b><ruby>留守<rt>るす</rt></ruby></b>" } ];
  const ALWAYS_EXCLUDE = new Set([ "挙句（に）", "道草（を）", "以上（は）", "人称（私）", "人称（あなた）", "矢先（に）" ]);
  const RUBY_EXCLUDE_PARTICLES = new Set([ "に", "は", "を", "が", "の", "と", "で", "から", "まで", "へ", "も", "や", "ね", "よ", "さ" ]);
  const REGEX_PATTERNS = {
    ruby: /([一-龯々]+)（([^（）]*)）/g,
    katakana: /([\u30A0-\u30FF]+)[（(]([\w\s+]+)[）)]/g,
    bracket: /[【「](?:.*?)([^【】「」（）・、\s]+)（([^（）]*)）([^【】「」（）]*)[】」]/g,
    kanaOnly: /^[\u3040-\u309F]+$/,
    nonKana: /[^\u3040-\u309F]/,
    isKanaChar: /^[\u3040-\u309F]$/,
    imgSrc: /(https:\/\/livedoor\.blogimg\.jp\/edewakaru\/imgs\/[a-z0-9]+\/[a-z0-9]+\/[a-z0-9]+)-s(\.jpg)/i
  };
  const REGEX_TESTERS = {
    isKanaOnly: text => REGEX_PATTERNS.kanaOnly.test(text),
    hasNonKana: text => REGEX_PATTERNS.nonKana.test(text),
    isKanaChar: char => REGEX_PATTERNS.isKanaChar.test(char)
  };
  const GLOBAL_REMOVE_SELECTORS = [ "header#blog-header", "footer#blog-footer", ".ldb_menu", ".article-social-btn", ".adsbygoogle", 'a[href*="blogmura.com"]', 'a[href*="with2.net"]' ];
  let PROCESSED_COMPOUND_WORDS = {
    segmentWords: new Map,
    replaceWords: new Map,
    globalRegex: null,
    patternResults: new Map
  };
  const SETTINGS_KEYS = {
    SCRIPT_ENABLED: "ruby_converter_enabled",
    FURIGANA_VISIBLE: "furigana_visible"
  };
  const SETTINGS_CONFIG = [ { key: SETTINGS_KEYS.SCRIPT_ENABLED, label: "ページ最適化", defaultValue: true, description: "ページの最適化とコンテンツクリーニング機能を有効にします", handler: handleScriptToggle }, { key: SETTINGS_KEYS.FURIGANA_VISIBLE, label: "振り仮名表示", defaultValue: true, description: "振り仮名の表示・非表示を切り替えます", handler: handleFuriganaToggle } ];
  function getSettingValue(key, defaultValue) {
    return GM_getValue(key, defaultValue);
  }
  function setSettingValue(key, value) {
    GM_setValue(key, value);
  }
  function handleScriptToggle(enabled) {
    setSettingValue(SETTINGS_KEYS.SCRIPT_ENABLED, enabled);
    showNotification("設定を保存しました。ページを再読み込みしてください。");
  }
  function handleFuriganaToggle(visible) {
    setSettingValue(SETTINGS_KEYS.FURIGANA_VISIBLE, visible);
    toggleFuriganaDisplay(visible);
  }
  function toggleFuriganaDisplay(visible) {
    const styleId = "furigana-display-style";
    let styleElement = document.getElementById(styleId);
    if (!styleElement) {
      styleElement = document.createElement("style");
      styleElement.id = styleId;
      document.head.appendChild(styleElement);
    }
    const displayValue = visible ? "ruby-text" : "none";
    styleElement.textContent = `rt { display: ${displayValue} !important; }`;
  }
  function showNotification(message) {
    const notification = document.createElement("div");
    notification.className = "settings-notification";
    notification.textContent = message;
    document.body.appendChild(notification);
    setTimeout(() => notification.remove(), 2e3);
  }
  function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  }
  function preprocessCompoundWords(rawWords) {
    const segmentWords = new Map;
    const replaceWords = new Map;
    const allPatterns = [];
    const patternResults = new Map;
    rawWords.forEach(entry => {
      const parsed = parseCompoundEntry(entry);
      if (!parsed) {
        return;
      }
      const firstChar = parsed.pattern[0];
      const escapedPattern = escapeRegExp(parsed.pattern);
      const regex = new RegExp(escapedPattern, "g");
      if (parsed.replacement) {
        if (!replaceWords.has(firstChar)) {
          replaceWords.set(firstChar, []);
        }
        const wordData = {
          pattern: parsed.pattern,
          replacement: parsed.replacement,
          regex: regex
        };
        replaceWords.get(firstChar).push(wordData);
        patternResults.set(parsed.pattern, parsed.replacement);
      } else if (parsed.kanji && parsed.reading) {
        if (!segmentWords.has(firstChar)) {
          segmentWords.set(firstChar, []);
        }
        const wordData = {
          pattern: parsed.pattern,
          kanji: parsed.kanji,
          reading: parsed.reading,
          regex: regex
        };
        segmentWords.get(firstChar).push(wordData);
        const precomputedResult = segmentCompoundWord(parsed.kanji, parsed.reading);
        patternResults.set(parsed.pattern, precomputedResult);
      }
      allPatterns.push(escapedPattern);
    });
    const globalRegex = allPatterns.length > 0 ? new RegExp(`(${allPatterns.join("|")})`, "g") : null;
    return {
      segmentWords: segmentWords,
      replaceWords: replaceWords,
      globalRegex: globalRegex,
      patternResults: patternResults
    };
  }
  function parseCompoundEntry(entry) {
    if (typeof entry === "string") {
      const leftIdx = entry.indexOf("（");
      const rightIdx = entry.lastIndexOf("）");
      if (leftIdx > 0 && rightIdx > leftIdx) {
        return {
          pattern: entry,
          kanji: entry.slice(0, leftIdx),
          reading: entry.slice(leftIdx + 1, rightIdx)
        };
      }
    } else if (entry && entry.reading) {
      return {
        pattern: entry.pattern,
        kanji: entry.pattern.replace(/（.*?）/, ""),
        reading: entry.reading
      };
    } else if (entry && entry.replacement) {
      return entry;
    }
    return null;
  }
  function segmentCompoundWord(kanji, reading) {
    let result = "";
    let kanjiIndex = 0;
    let readingIndex = 0;
    while (kanjiIndex < kanji.length) {
      if (REGEX_TESTERS.isKanaChar(kanji[kanjiIndex])) {
        result += kanji[kanjiIndex];
        kanjiIndex++;
        readingIndex = reading.indexOf(kanji[kanjiIndex - 1], readingIndex) + 1;
      } else {
        let kanjiPart = "";
        let readingPart = "";
        while (kanjiIndex < kanji.length && !REGEX_TESTERS.isKanaChar(kanji[kanjiIndex])) {
          kanjiPart += kanji[kanjiIndex];
          kanjiIndex++;
        }
        const nextKanaIndex = kanjiIndex < kanji.length ? reading.indexOf(kanji[kanjiIndex], readingIndex) : reading.length;
        readingPart = reading.substring(readingIndex, nextKanaIndex);
        readingIndex = nextKanaIndex;
        result += `<ruby>${kanjiPart}<rt>${readingPart}</rt></ruby>`;
      }
    }
    return result;
  }
  function processTextContent(text) {
    if (!text.includes("（") && !text.includes("(")) {
      return text;
    }
    if (PROCESSED_COMPOUND_WORDS.globalRegex) {
      text = text.replace(PROCESSED_COMPOUND_WORDS.globalRegex, match => {
        const result = PROCESSED_COMPOUND_WORDS.patternResults.get(match);
        return result || match;
      });
    }
    text = text.replace(REGEX_PATTERNS.katakana, (_, katakana, romaji) => `<ruby>${katakana}<rt>${romaji}</rt></ruby>`);
    return text.replace(REGEX_PATTERNS.ruby, (_, kanji, reading) => {
      const fullMatch = kanji + "（" + reading + "）";
      if (ALWAYS_EXCLUDE.has(fullMatch)) {
        return _;
      }
      if (RUBY_EXCLUDE_PARTICLES.has(reading) && REGEX_TESTERS.isKanaOnly(kanji)) {
        return _;
      }
      if (REGEX_TESTERS.hasNonKana(reading)) {
        return _;
      }
      return reading ? `<ruby>${kanji}<rt>${reading}</rt></ruby>` : _;
    });
  }
  function trimContainerBreaks(container) {
    let node = container.firstChild;
    while (node) {
      const nextNode = node.nextSibling;
      if (node.nodeType === Node.TEXT_NODE && /^\s*$/.test(node.textContent) || node.nodeType === Node.ELEMENT_NODE && node.tagName === "BR" || node.nodeType === Node.ELEMENT_NODE && node.innerHTML === "&nbsp;" && node.tagName === "SPAN") {
        container.removeChild(node);
      } else {
        break;
      }
      node = nextNode;
    }
    node = container.lastChild;
    while (node) {
      const prevNode = node.previousSibling;
      if (node.nodeType === Node.TEXT_NODE && /^\s*$/.test(node.textContent) || node.nodeType === Node.ELEMENT_NODE && node.tagName === "BR" || node.nodeType === Node.ELEMENT_NODE && node.innerHTML === "&nbsp;" && node.tagName === "SPAN") {
        container.removeChild(node);
      } else {
        break;
      }
      node = prevNode;
    }
  }
  function processImageLinks(container) {
    const imageLinks = container.querySelectorAll('a[href*="livedoor.blogimg.jp"]');
    imageLinks.forEach(link => {
      const img = link.querySelector("img.pict");
      if (!img) {
        return;
      }
      const originalSrc = img.src.replace(REGEX_PATTERNS.imgSrc, "$1$2");
      const newImg = document.createElement("img");
      newImg.src = originalSrc;
      newImg.alt = (img.alt || "").replace(/blog/gi, "");
      newImg.className = img.className;
      newImg.width = img.width;
      newImg.height = img.height;
      link.replaceWith(newImg);
    });
  }
  function removeGlobalElements() {
    const combinedSelector = GLOBAL_REMOVE_SELECTORS.join(",");
    const elementsToRemove = document.querySelectorAll(combinedSelector);
    elementsToRemove.forEach(el => el.remove());
  }
  function cleanupContent(container) {
    processImageLinks(container);
    const elementsToRemove = [];
    const unwantedLinks = container.querySelectorAll('a[href*="blogmura.com"], a[href*="with2.net"]');
    elementsToRemove.push(...unwantedLinks);
    const scripts = container.querySelectorAll("script");
    elementsToRemove.push(...scripts);
    const adDiv = container.querySelector("#ad2");
    if (adDiv) {
      let nextElement = adDiv.nextElementSibling;
      while (nextElement) {
        elementsToRemove.push(nextElement);
        nextElement = nextElement.nextElementSibling;
      }
      elementsToRemove.push(adDiv);
    }
    elementsToRemove.forEach(el => el.remove());
    trimContainerBreaks(container);
  }
  const DYNAMIC_COMPOUND_WORDS = new Set;
  function findAndRegisterCompounds(element) {
    if (!element) {
      return;
    }
    const htmlContent = element.innerHTML;
    let match;
    while ((match = REGEX_PATTERNS.bracket.exec(htmlContent)) !== null) {
      const reading = match[2];
      if (reading && !REGEX_TESTERS.hasNonKana(reading)) {
        const compound = match[1] + "（" + match[2] + "）" + match[3];
        if (!DYNAMIC_COMPOUND_WORDS.has(compound)) {
          DYNAMIC_COMPOUND_WORDS.add(compound);
          addDynamicCompoundWord(compound);
        }
      }
    }
  }
  function addDynamicCompoundWord(compound) {
    const parsed = parseCompoundEntry(compound);
    if (!parsed) {
      return;
    }
    const firstChar = parsed.pattern[0];
    const escapedPattern = escapeRegExp(parsed.pattern);
    if (parsed.kanji && parsed.reading) {
      if (!PROCESSED_COMPOUND_WORDS.segmentWords.has(firstChar)) {
        PROCESSED_COMPOUND_WORDS.segmentWords.set(firstChar, []);
      }
      const wordData = {
        pattern: parsed.pattern,
        kanji: parsed.kanji,
        reading: parsed.reading,
        regex: new RegExp(escapedPattern, "g")
      };
      PROCESSED_COMPOUND_WORDS.segmentWords.get(firstChar).push(wordData);
      const precomputedResult = segmentCompoundWord(parsed.kanji, parsed.reading);
      PROCESSED_COMPOUND_WORDS.patternResults.set(parsed.pattern, precomputedResult);
      rebuildGlobalRegex();
    }
  }
  function rebuildGlobalRegex() {
    const allPatterns = [];
    for (const words of PROCESSED_COMPOUND_WORDS.segmentWords.values()) {
      for (const word of words) {
        allPatterns.push(escapeRegExp(word.pattern));
      }
    }
    for (const words of PROCESSED_COMPOUND_WORDS.replaceWords.values()) {
      for (const word of words) {
        allPatterns.push(escapeRegExp(word.pattern));
      }
    }
    PROCESSED_COMPOUND_WORDS.globalRegex = allPatterns.length > 0 ? new RegExp(`(${allPatterns.join("|")})`, "g") : null;
  }
  function processRubyConversion(root) {
    const treeWalker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT, {
      acceptNode: node => node.parentNode.nodeName !== "SCRIPT" && node.parentNode.nodeName !== "STYLE" ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT
    });
    const nodesToProcess = [];
    let node;
    while (node = treeWalker.nextNode()) {
      const newContent = processTextContent(node.nodeValue);
      if (newContent !== node.nodeValue) {
        nodesToProcess.push({
          node: node,
          newContent: newContent
        });
      }
    }
    if (nodesToProcess.length > 0) {
      const groupedByParent = new Map;
      nodesToProcess.forEach(({node: node, newContent: newContent}) => {
        const parent = node.parentNode;
        if (!groupedByParent.has(parent)) {
          groupedByParent.set(parent, []);
        }
        groupedByParent.get(parent).push({
          node: node,
          newContent: newContent
        });
      });
      groupedByParent.forEach((nodeGroup, parent) => {
        nodeGroup.sort((a, b) => {
          const position = a.node.compareDocumentPosition(b.node);
          return position & Node.DOCUMENT_POSITION_FOLLOWING ? -1 : 1;
        });
        for (let i = nodeGroup.length - 1; i >= 0; i--) {
          const {node: node, newContent: newContent} = nodeGroup[i];
          const fragment = document.createDocumentFragment();
          const tempDiv = document.createElement("div");
          tempDiv.innerHTML = newContent;
          while (tempDiv.firstChild) {
            fragment.appendChild(tempDiv.firstChild);
          }
          parent.insertBefore(fragment, node);
          parent.removeChild(node);
        }
      });
    }
  }
  function applyHtmlReplacements(element, rules) {
    if (!element || !rules || rules.length === 0) {
      return;
    }
    let currentHTML = element.innerHTML;
    const originalHTML = currentHTML;
    rules.forEach(rule => {
      currentHTML = currentHTML.replace(rule.pattern, rule.replacement);
    });
    if (currentHTML !== originalHTML) {
      element.innerHTML = currentHTML;
    }
  }
  function optimizeSidebar() {
    const sidebar = document.querySelector("aside#sidebar");
    if (!sidebar) {
      return;
    }
    const category = sidebar.querySelector(".plugin-categorize");
    sidebar.textContent = "";
    if (category) {
      sidebar.appendChild(category);
      sidebar.style.visibility = "visible";
    }
  }
  function injectUnifiedToggleStyles() {
    const css = `\n      #settings-panel { position: fixed; bottom: 1.3rem; right: 1rem; z-index: 9999; display: flex; flex-direction: column; gap: 0.5rem; padding: 1rem; background: white; border-radius: 4px; box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1),0 4px 6px -2px rgba(0,0,0,0.05); width: 140px; opacity: 0.8; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; }\n      .settings-title { font-size: 0.875rem; font-weight: 600; color: #1F2937; margin: 0 0 0.375rem 0; text-align: center; border-bottom: 1px solid #E5E7EB; padding-bottom: 0.375rem; }\n      .setting-item { display: flex; align-items: center; justify-content: space-between; gap: 0.5rem; }\n      .setting-label { font-size: 0.8125rem; font-weight: 500; color: #4B5563; cursor: pointer; flex: 1; line-height: 1.2; }\n      .toggle-switch { position: relative; display: inline-block; width: 2.5rem; height: 1.25rem; flex-shrink: 0; }\n      .toggle-switch input { opacity: 0; width: 0; height: 0; }\n      .toggle-slider { position: absolute; cursor: pointer; top: 0; left: 0; right: 0; bottom: 0; background-color: #E5E7EB; transition: all 0.2s ease-in-out; border-radius: 9999px; }\n      .toggle-slider:before { position: absolute; content: ""; height: 0.9375rem; width: 0.9375rem; left: 0.15625rem; bottom: 0.15625rem; background-color: white; transition: all 0.2s ease-in-out; border-radius: 50%; box-shadow: 0 1px 3px 0 rgba(0,0,0,0.1),0 1px 2px 0 rgba(0,0,0,0.06); }\n      input:checked+.toggle-slider { background-color: #3B82F6; }\n      input:checked+.toggle-slider:before { transform: translateX(1.25rem); }\n      .toggle-slider:hover { background-color: #D1D5DB; }\n      input:checked+.toggle-slider:hover { background-color: #2563EB; }\n      .settings-notification { position: fixed; bottom: 9rem; right: 1rem; z-index: 10000; padding: 0.5rem 0.75rem; background-color: #3B82F6; color: white; border-radius: 0.375rem; box-shadow: 0 10px 15px -3px rgba(0,0,0,0.1),0 4px 6px -2px rgba(0,0,0,0.05); font-size: 0.8125rem; font-weight: 500; animation: slideInOut 3s ease-in-out; white-space: nowrap; }\n      @keyframes slideInOut { 0% { opacity: 0; transform: translateX(20px); } 15% { opacity: 1; transform: translateX(0); } 85% { opacity: 1; transform: translateX(0); } 100% { opacity: 0; transform: translateX(20px); } }\n    `;
    const style = document.createElement("style");
    style.textContent = css;
    (document.head || document.documentElement).appendChild(style);
  }
  function injectInitialStyles() {
    const css = `\n      #container { width: 100%; }\n      @media (min-width: 960px) { #container { max-width: 960px; } }\n      @media (min-width: 1040px) { #container { max-width: 1040px; } }\n      #content { display: flex; position: relative; padding: 50px 0 !important; }\n      #main { flex: 1; float: none !important; width: 100% !important; }\n      aside#sidebar { visibility: hidden; float: none !important; width: 350px !important; flex: 0 0 350px; }\n      .plugin-categorize { position: fixed; height: 85vh; display: flex; flex-direction: column; padding: 0 !important; width: 350px !important; }\n      .plugin-categorize .side { flex: 1; overflow-y: auto; max-height: unset; }\n      .plugin-categorize .side > :not([hidden]) ~ :not([hidden]) { margin-top: 5px; margin-bottom: 0; }\n      .article { padding: 0 0 20px 0 !important; margin-bottom: 30px !important; }\n      .article-body { padding: 0 !important; }\n      .article-pager { margin-bottom: 0 !important; }\n      .article-body-inner { line-height: 2; }\n      .article-body-inner img.pict { margin: 0 !important; width: 80% !important; display: block; }\n      .article-body-inner strike { color: orange; }\n      .article-body-inner iframe { margin: 4px 0 !important; }\n      .to-pagetop { position: fixed; bottom: 1.2rem; right: 220px; z-index: 1000; }\n      rt, iframe, .pager { -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; user-select: none; }\n      header#blog-header, footer#blog-footer, .ldb_menu, .article-social-btn, .adsbygoogle, #ldblog_related_articles_01d4ecf1, #ad2 { display: none !important; }\n      .article-body-inner:after, .article-meta:after, #container:after, #content:after, article:after, section:after, .cf:after { content: none !important; display: none !important; height: auto !important; visibility: visible !important; }\n    `;
    if (typeof GM_addStyle === "function") {
      GM_addStyle(css);
    } else {
      const style = document.createElement("style");
      style.textContent = css;
      (document.head || document.documentElement).appendChild(style);
    }
  }
  function createUnifiedSettingsPanel() {
    const panel = document.createElement("div");
    panel.id = "settings-panel";
    const title = document.createElement("h3");
    title.className = "settings-title";
    title.textContent = "設定パネル";
    panel.appendChild(title);
    SETTINGS_CONFIG.forEach(config => {
      const settingItem = createSettingToggle(config);
      panel.appendChild(settingItem);
    });
    document.body.appendChild(panel);
  }
  function createSettingToggle(config) {
    const currentValue = getSettingValue(config.key, config.defaultValue);
    const settingId = `setting-${config.key.replace(/_/g, "-")}`;
    const container = document.createElement("div");
    container.className = "setting-item";
    container.innerHTML = `\n      <label for="${settingId}" class="setting-label" title="${config.description}">\n        ${config.label}\n      </label>\n      <label class="toggle-switch">\n        <input type="checkbox" id="${settingId}" ${currentValue ? "checked" : ""}>\n        <span class="toggle-slider"></span>\n      </label>\n    `;
    const checkbox = container.querySelector("input");
    checkbox.addEventListener("change", e => {
      config.handler(e.target.checked);
    });
    return container;
  }
  function initializeFuriganaDisplay() {
    const furiganaVisible = getSettingValue(SETTINGS_KEYS.FURIGANA_VISIBLE, true);
    if (!furiganaVisible) {
      updateFuriganaGlobalStyle(false);
    }
  }
  if (!getSettingValue(SETTINGS_KEYS.SCRIPT_ENABLED, true)) {
    injectUnifiedToggleStyles();
    if (document.readyState === "complete") {
      createUnifiedSettingsPanel();
    } else {
      document.addEventListener("DOMContentLoaded", createUnifiedSettingsPanel);
    }
    return;
  }
  injectUnifiedToggleStyles();
  injectInitialStyles();
  PROCESSED_COMPOUND_WORDS = preprocessCompoundWords(RAW_COMPOUND_WORDS);
  function main() {
    removeGlobalElements();
    const processMainContent = () => {
      const articleBodies = document.querySelectorAll(".article-body-inner");
      if (articleBodies.length === 0) {
        return;
      }
      let currentIndex = 0;
      const processBatch = () => {
        const batchSize = Math.min(2, articleBodies.length - currentIndex);
        const endIndex = currentIndex + batchSize;
        for (let i = currentIndex; i < endIndex; i++) {
          const body = articleBodies[i];
          cleanupContent(body);
          applyHtmlReplacements(body, HTML_REPLACEMENT_RULES);
          findAndRegisterCompounds(body);
          processRubyConversion(body);
          body.style.opacity = 1;
        }
        currentIndex = endIndex;
        if (currentIndex < articleBodies.length) {
          requestAnimationFrame(processBatch);
        } else {
          optimizeSidebar();
        }
      };
      processBatch();
    };
    const tryProcessContent = (retryCount = 0) => {
      const articleBodies = document.querySelectorAll(".article-body-inner");
      if (articleBodies.length > 0) {
        processMainContent();
      } else if (retryCount < 3) {
        setTimeout(() => tryProcessContent(retryCount + 1), 100);
      }
    };
    tryProcessContent();
  }
  function initializeScript() {
    main();
    createUnifiedSettingsPanel();
    initializeFuriganaDisplay();
  }
  if (document.readyState === "loading") {
    document.addEventListener("DOMContentLoaded", initializeScript, {
      once: true
    });
  } else {
    initializeScript();
  }
})();