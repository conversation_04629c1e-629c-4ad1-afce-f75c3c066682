// ==UserScript==
// @name         Japanese Ruby Text Converter
// @namespace    http://tampermonkey.net/
// @version      3.6
// @description  Convert Japanese text with readings from parentheses format to ruby tags with compound word segmentation
// <AUTHOR>
// @match        https://www.edewakaru.com/*
// @grant        GM_addStyle
// @grant        GM_getValue
// @grant        GM_setValue
// @run-at       document-start
// ==/UserScript==

;(function () {
  ;('use strict')

  // --- 配置区域 ---
  // 复合词列表（支持多种格式）
  const COMPOUND_WORDS = [
    // 标准格式：汉字（注音）
    '長い間（ながいあいだ）',
    '座り心地（すわりごこち）',
    '触り心地（さわりごこち）',
    '三度目の正直（さんどめのしょうじき）',
    '申し訳（もうしわけ）',
    '濡れ衣を着せる（ぬれぎぬをきせる）',
    '頭が切れる（あたまがきれる）',
    '目の色が変わる（めのいろがかわる）',
    '目の色を変える（めのいろをかえる）',
    '出張（しゅっちょう）',
    '羽根を伸ばす（はねをのばす）',
    '口を出す（くちをだす）',
    '耳を揃える（みみをそろえる）',
    '目に入れても痛くない（めにいれてもいたくない）',
    '目玉が飛び出る（めだまがとびでる）',
    '手に付かない（てにつかない）',
    '長蛇の列（ちょうだのれつ）',
    '大好き（だいすき）',
    '目に入る（めにはいる）',
    '唐揚げ（からあげ）',
    '小耳に挟む（こみみにはさむ）',
    '瓜二つ（うりふたつ）',
    '顔に書いてある（かおにかいてある）',
    '道草を食う（みちくさをくう）',
    '立ち読み（たちよみ）',
    '１杯（いっぱい）',
    '１回（いっかい）',
    '１泊（いっぱく）',
    '試験（しけん）',
    '案の定（あんのじょう）',

    // 强制注音
    { pattern: '羽根を伸ばす（羽根を伸ばす）', reading: 'はねをのばす' },
    { pattern: '長蛇の列（長蛇の列）', reading: 'ちょうだのれつ' },
    //{ pattern: '１回試験（しけん）', reading: 'いっかいしけん' },

    // 强制替换
    { pattern: '言い方（いいかた）', replacement: '<ruby>言<rt>い</rt></ruby>い<ruby>方<rt>かた</rt></ruby>' },
    {
      pattern: '目の色が変わる・目の色を変える（めのいろがかわる・かえる）',
      replacement:
        '<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>が<ruby>変<rt>かわ</rt></ruby>る・<ruby>目<rt>め</rt></ruby>の<ruby>色<rt>いろ</rt></ruby>を<ruby>変<rt>かえ</rt></ruby>える',
    },
    {
      pattern: '意味で（いみ）',
      replacement: '<ruby>意味<rt>いみ</rt></ruby>で',
    },
  ]

  // 始终不转换的注音模式
  const ALWAYS_EXCLUDE = new Set(['挙句（に）', '道草（を）', '以上（は）'])
  // 助词排除列表，且前面非汉字。适用于 'あげく（に）' 不转换
  const RUBY_EXCLUDE_PARTICLES = new Set(['に', 'は', 'を', 'が', 'の', 'と', 'で', 'から', 'まで', 'へ', 'も', 'や', 'ね', 'よ', 'さ'])

  // 正则表达式匹配注音格式：汉字（平假名）
  const RUBY_REGEX = /([一-龯々]+)（([^（）]*)）/g
  // 正则表达式匹配片假名注音格式：片假名（拉丁字母）
  const KATAKANA_RUBY_REGEX = /([\u30A0-\u30FF]+)[（(]([a-zA-Z+]+)[）)]/g
  // 全平假名
  const KANA_ONLY_REGEX = /^[\u3040-\u309F]+$/
  // 非平假名字符
  const NON_KANA_REGEX = /[^\u3040-\u309F]/
  // 单个平假名字符
  const IS_KANA_CHAR_REGEX = /^[\u3040-\u309F]$/
  // 图片链接正则，匹配 livedoor.blogimg.jp 的图片链接
  const IMG_SRC_REGEX = /(https:\/\/livedoor\.blogimg\.jp\/edewakaru\/imgs\/[a-z0-9]+\/[a-z0-9]+\/[a-z0-9]+)-s(\.jpg)/i
  // 全局移除的选择器
  const GLOBAL_REMOVE_SELECTORS = [
    'header#blog-header',
    'footer#blog-footer',
    '.ldb_menu',
    '.article-social-btn',
    '.adsbygoogle',
    'a[href*="blogmura.com"]',
    'a[href*="with2.net"]',
  ]

  // --- 开关控制功能 (始终执行) ---
  const TOGGLE_KEY = 'ruby_converter_enabled'

  // 注入样式
  ;(function injectToggleStyles() {
    const css = `
      /* 开关容器 */
      #ruby-converter-toggle {
        position: fixed;
        top: 1rem;
        right: 1rem;
        z-index: 9999;
        display: flex;
        align-items: center;
        gap: 0.5rem;
        padding: 0.5rem;
        background: white;
        border-radius: 0.375rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      }

      /* 开关标签 */
      #ruby-converter-toggle label {
        font-size: 0.875rem;
        font-weight: 500;
        color: #4B5563;
        cursor: pointer;
      }

      /* 开关主体 */
      .toggle-switch {
        position: relative;
        display: inline-block;
        width: 3rem;
        height: 1.5rem;
      }

      /* 隐藏原始复选框 */
      .toggle-switch input {
        opacity: 0;
        width: 0;
        height: 0;
      }

      /* 开关滑块 */
      .toggle-slider {
        position: absolute;
        cursor: pointer;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #E5E7EB;
        transition: .2s;
        border-radius: 9999px;
      }

      /* 开关圆形按钮 */
      .toggle-slider:before {
        position: absolute;
        content: "";
        height: 1.125rem;
        width: 1.125rem;
        left: 0.1875rem;
        bottom: 0.1875rem;
        background-color: white;
        transition: .2s;
        border-radius: 50%;
        box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
      }

      /* 开启状态 */
      input:checked + .toggle-slider {
        background-color: #3B82F6;
      }

      input:checked + .toggle-slider:before {
        transform: translateX(1.5rem);
      }

      /* 提示信息 */
      .ruby-converter-notification {
        position: fixed;
        top: 4rem;
        right: 1rem;
        z-index: 10000;
        padding: 0.75rem 1rem;
        background-color: #3B82F6;
        color: white;
        border-radius: 0.375rem;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        font-size: 0.875rem;
        font-weight: 500;
        animation: fadeInOut 2s ease-in-out;
      }

      @keyframes fadeInOut {
        0% { opacity: 0; transform: translateY(-10px); }
        20% { opacity: 1; transform: translateY(0); }
        80% { opacity: 1; transform: translateY(0); }
        100% { opacity: 0; transform: translateY(-10px); }
      }
    `
    const style = document.createElement('style')
    style.textContent = css
    ;(document.head || document.documentElement).appendChild(style)
  })()

  // 创建开关元素
  function createToggle() {
    const enabled = GM_getValue(TOGGLE_KEY, true)
    const container = document.createElement('div')
    container.id = 'ruby-converter-toggle'
    container.innerHTML = `
      <label for="ruby-toggle-switch">注音変換</label>
      <label class="toggle-switch">
        <input type="checkbox" id="ruby-toggle-switch" ${enabled ? 'checked' : ''}>
        <span class="toggle-slider"></span>
      </label>
    `
    document.body.appendChild(container)
    // 添加切换事件
    container.querySelector('input').addEventListener('change', (e) => {
      GM_setValue(TOGGLE_KEY, e.target.checked)
      // 显示提示
      const msg = document.createElement('div')
      msg.className = 'ruby-converter-notification'
      msg.textContent = '設定を保存しました。ページを更新してください。'
      document.body.appendChild(msg)
      setTimeout(() => msg.remove(), 2000)
    })
  }

  // --- 主功能 (仅在开关开启时执行) ---
  if (!GM_getValue(TOGGLE_KEY, true)) {
    if (document.readyState === 'complete') {
      createToggle()
    } else {
      document.addEventListener('DOMContentLoaded', createToggle)
    }
    return
  }

  // --- 第一步：尽早注入 CSS 防止闪烁 ---
  ;(function injectInitialStyles() {
    const css = `
      /* 基本布局 */
      #container {
        width: 100%;
      }
      @media (min-width: 980px) {
        #container {
          max-width: 980px;
        }
      }
      #content {
        display: flex;
        position: relative;
        padding: 50px 0 !important;
      }
      #main {
        flex: 1;
        float: none !important;
        width: 100% !important;
      }

      /* 侧边栏 */
      aside#sidebar {
        visibility: hidden;
        float: none !important;
        width: 350px !important;
        flex: 0 0 350px;
      }
      .plugin-categorize {
        position: fixed;
        height: 85vh;
        display: flex;
        flex-direction: column;
        padding: 0 !important;
        width: 350px !important;
      }
      .plugin-categorize .side {
        flex: 1;
        overflow-y: auto;
        max-height: unset;
      }
      .plugin-categorize .side > :not([hidden]) ~ :not([hidden]) {
        margin-top: 5px;
        margin-bottom: 0;
      }

      /* 文章内容 */
      .article {
        padding: 20px 0 !important;
      }
      .article-pager {
        margin-bottom: 0 !important;
      }
      .article-body-inner {
        line-height: 2;
      }
      .article-body-inner img.pict {
        margin: 0 !important;
        width: 90% !important;
      }

      /* 返回顶部按钮 */
      .to-pagetop {
        position: fixed;
        bottom: 20px;
        right: 100px;
        z-index: 1000;
      }

      /* 禁用文本选择和鼠标事件 */
      rt {
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        pointer-events: none;
      }

      iframe {
        margin: 4px 0 !important;
      }

      /* 隐藏不必要的元素 */
      header#blog-header,
      footer#blog-footer,
      .ldb_menu,
      .article-social-btn,
      .adsbygoogle,
      #ldblog_related_articles_01d4ecf1,
      #ad2 {
        display: none !important;
      }

      /* 覆盖清除浮动样式 */
      .article-body-inner:after,
      .article-meta:after,
      #container:after,
      #content:after,
      article:after,
      section:after,
      .cf:after {
        content: none !important;
        display: none !important;
        height: auto !important;
        visibility: visible !important;
      }

      .sub-paragraph {
  margin-bottom: 1.5em; /* 小段落之间的间距 */
  padding-left: 1em;
  border-left: 2px solid #D1D5DB; /* 给小段落一个更细的左边框以区分 */
}
.sub-paragraph p {
  margin-bottom: 0.5em; /* 小段落内部的行间距可以小一些 */
}
.sub-paragraph p:last-child {
  margin-bottom: 0;
}
    `
    if (typeof GM_addStyle === 'function') {
      GM_addStyle(css)
    } else {
      const style = document.createElement('style')
      style.textContent = css
      ;(document.head || document.documentElement).appendChild(style)
    }
  })()

  // --- 复合词处理函数 ---
  function parseCompoundEntry(entry) {
    // 字符串格式："汉字（注音）"
    if (typeof entry === 'string') {
      const leftIdx = entry.indexOf('（')
      const rightIdx = entry.lastIndexOf('）')
      if (leftIdx > 0 && rightIdx > leftIdx) {
        return {
          pattern: entry,
          kanji: entry.slice(0, leftIdx),
          reading: entry.slice(leftIdx + 1, rightIdx),
        }
      }
    }
    // 对象格式：{ pattern: '...', reading: '...' }
    else if (entry && entry.reading) {
      return {
        pattern: entry.pattern,
        kanji: entry.pattern.replace(/（.*?）/, ''),
        reading: entry.reading,
      }
    }
    // 对象格式：{ pattern: '...', replacement: '...' }
    else if (entry && entry.replacement) {
      return entry
    }
    return null
  }

  function segmentCompoundWord(kanji, reading) {
    const segments = []
    let kanjiIndex = 0
    let readingIndex = 0

    while (kanjiIndex < kanji.length) {
      // 检查当前字符是否为平假名
      if (IS_KANA_CHAR_REGEX.test(kanji[kanjiIndex])) {
        // 如果是平假名，直接添加到结果中
        segments.push(kanji[kanjiIndex])
        kanjiIndex++
        // 在注音中找到对应的平假名位置
        readingIndex = reading.indexOf(kanji[kanjiIndex - 1], readingIndex) + 1
      } else {
        // 处理连续的汉字部分
        let kanjiPart = ''
        let readingPart = ''

        // 收集连续的汉字
        while (kanjiIndex < kanji.length && !IS_KANA_CHAR_REGEX.test(kanji[kanjiIndex])) {
          kanjiPart += kanji[kanjiIndex]
          kanjiIndex++
        }

        // 确定对应的注音部分
        const nextKanaIndex = kanjiIndex < kanji.length ? reading.indexOf(kanji[kanjiIndex], readingIndex) : reading.length

        readingPart = reading.substring(readingIndex, nextKanaIndex)
        readingIndex = nextKanaIndex

        // 创建ruby标签
        segments.push(`<ruby>${kanjiPart}<rt>${readingPart}</rt></ruby>`)
      }
    }

    return segments.join('')
  }

  // --- 全局元素处理 ---
  function removeGlobalElements() {
    GLOBAL_REMOVE_SELECTORS.forEach((selector) => {
      document.querySelectorAll(selector).forEach((el) => el?.remove())
    })
  }

  // --- 图片链接处理函数 ---
  function processImageLinks(container) {
    const imageLinks = container.querySelectorAll('a[href*="livedoor.blogimg.jp"]')
    imageLinks.forEach((link) => {
      const img = link.querySelector('img.pict')
      if (!img) return
      // 获取原始图片 URL（移除 -s 后缀）
      const originalSrc = img.src.replace(IMG_SRC_REGEX, '$1$2')
      // 创建新的图片元素
      const newImg = document.createElement('img')
      newImg.src = originalSrc
      newImg.alt = (img.alt || '').replace(/blog/gi, '')
      newImg.className = img.className
      newImg.width = img.width
      newImg.height = img.height
      // 替换链接为图片
      link.replaceWith(newImg)
    })
  }

  // --- 清理容器开头和结尾的空白和换行 ---
  function trimContainerBreaks(container) {
    // 清理开头的空白
    let node = container.firstChild
    while (node) {
      const nextNode = node.nextSibling
      if (
        (node.nodeType === Node.TEXT_NODE && /^\s*$/.test(node.textContent)) ||
        (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'BR') ||
        (node.nodeType === Node.ELEMENT_NODE && node.innerHTML === '&nbsp;' && node.tagName === 'SPAN')
      ) {
        container.removeChild(node)
      } else {
        break // 遇到非空白内容时停止
      }
      node = nextNode
    }

    // 清理结尾的空白
    node = container.lastChild
    while (node) {
      const prevNode = node.previousSibling
      if (
        (node.nodeType === Node.TEXT_NODE && /^\s*$/.test(node.textContent)) ||
        (node.nodeType === Node.ELEMENT_NODE && node.tagName === 'BR') ||
        (node.nodeType === Node.ELEMENT_NODE && node.innerHTML === '&nbsp;' && node.tagName === 'SPAN')
      ) {
        container.removeChild(node)
      } else {
        break // 遇到非空白内容时停止
      }
      node = prevNode
    }
  }

  // --- 文章内容处理 ---
  function cleanupContent(container) {
    // 0. 处理图片链接
    processImageLinks(container)
    // 1. 移除所有不需要的链接
    container.querySelectorAll('a[href*="blogmura.com"], a[href*="with2.net"]').forEach((el) => el.remove())
    // 2. 查找 ad2 元素
    const adDiv = container.querySelector('#ad2')
    if (adDiv) {
      // 3. 移除 ad2 之后的所有元素
      let nextElement = adDiv.nextElementSibling
      while (nextElement) {
        const toRemove = nextElement
        nextElement = nextElement.nextElementSibling
        toRemove.remove()
      }
      // 4. 移除 ad2 本身
      adDiv.remove()
    }
    // 5. 移除所有脚本元素
    container.querySelectorAll('script').forEach((script) => script.remove())
    // 6. 清理容器末尾的空白和换行
    trimContainerBreaks(container)
  }

  // 正则特殊字符转义函数
  function escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  // --- 文本内容处理 ---
  function processTextContent(text) {
    // 处理复合词（支持多种格式）
    COMPOUND_WORDS.forEach((compound) => {
      const parsed = parseCompoundEntry(compound)
      if (!parsed) return

      // 直接替换型
      if (parsed.replacement) {
        const pattern = escapeRegExp(parsed.pattern)
        text = text.replace(new RegExp(pattern, 'g'), parsed.replacement)
      }
      // 注音解析型
      else if (parsed.kanji && parsed.reading) {
        const pattern = escapeRegExp(parsed.pattern)
        const replacement = segmentCompoundWord(parsed.kanji, parsed.reading)
        text = text.replace(new RegExp(pattern, 'g'), replacement)
      }
    })

    // 处理常规注音

    // 1. 片假名+英文注音处理（支持全角/半角括号）
    text = text.replace(KATAKANA_RUBY_REGEX, (_, katakana, romaji) => {
      // 直接整体作为注音，不拆分 +
      return `<ruby>${katakana}<rt>${romaji}</rt></ruby>`
    })

    // 2. 汉字（平假名）注音处理
    return text.replace(RUBY_REGEX, (_, kanji, reading) => {
      const fullMatch = kanji + '（' + reading + '）'

      // 检查是否在始终不转换列表中
      if (ALWAYS_EXCLUDE.has(fullMatch)) {
        return _
      }

      // 排除助词且 kanji 全为平假名
      if (RUBY_EXCLUDE_PARTICLES.has(reading) && KANA_ONLY_REGEX.test(kanji)) return _
      // 排除非假名注音
      if (NON_KANA_REGEX.test(reading)) return _

      return reading ? `<ruby>${kanji}<rt>${reading}</rt></ruby>` : _
    })
  }

  // --- Ruby 转换处理 ---
  function processRubyConversion(root) {
    const treeWalker = document.createTreeWalker(root, NodeFilter.SHOW_TEXT, {
      acceptNode: (node) => (node.parentNode.nodeName !== 'SCRIPT' && node.parentNode.nodeName !== 'STYLE' ? NodeFilter.FILTER_ACCEPT : NodeFilter.FILTER_REJECT),
    })
    // 收集所有文本节点
    const textNodes = []
    while (treeWalker.nextNode()) {
      textNodes.push(treeWalker.currentNode)
    }
    // 遍历所有文本节点并处理内容
    textNodes.forEach((node) => {
      const newContent = processTextContent(node.nodeValue)
      if (newContent !== node.nodeValue) {
        const wrapper = document.createElement('span')
        wrapper.innerHTML = newContent
        node.replaceWith(...wrapper.childNodes)
      }
    })
  }

  // --- 侧边栏优化 ---
  function optimizeSidebar() {
    const sidebar = document.querySelector('aside#sidebar')
    if (!sidebar) return

    const category = sidebar.querySelector('.plugin-categorize')
    sidebar.textContent = ''
    if (category) {
      sidebar.appendChild(category)
      // 显示处理完成的侧边栏
      sidebar.style.visibility = 'visible'
    }
  }

  /**
   * 核心函数：处理单个模块内部的内容，将其结构化为小段落组和段落
   * @param {string} htmlContent - 模块内部的原始HTML
   * @returns {string} - 处理后的HTML字符串
   */
  function processModuleContent(htmlContent) {
    // 1. 定义小段落的起始标记
    const subParagraphMarkerRegex = /^[\s\u00A0]*[①②③④⑤⑥⑦⑧⑨⑩]/
    // 独特标记，用于安全地分割<br>
    const breakMarker = '%%BR%%'

    // 将一个或多个<br>替换为单个标记
    const lines = htmlContent.replace(/(<br\s*\/?>\s*)+/g, breakMarker).split(breakMarker)

    let resultHtml = ''
    let currentSubParagraphLines = [] // 用于收集属于同一个小段落的行

    // “冲洗”函数：将收集到的行处理并包裹成一个小段落组
    const flushSubParagraph = () => {
      if (currentSubParagraphLines.length > 0) {
        const content = currentSubParagraphLines.map((line) => `<p>${line}</p>`).join('')
        resultHtml += `<div class="sub-paragraph">${content}</div>`
        currentSubParagraphLines = [] // 清空，准备下一个
      }
    }

    for (const line of lines) {
      const trimmedLine = line.trim()

      // Point 3: 彻底过滤空行，包括只含&nbsp;的行
      if (!trimmedLine || trimmedLine.replace(/&nbsp;/g, '').trim().length === 0) {
        continue
      }

      // Point 4: 检查是否为小段落的开始
      // 我们需要剥离HTML标签来检查文本内容
      const tempDiv = document.createElement('div')
      tempDiv.innerHTML = trimmedLine
      const textContent = tempDiv.textContent || tempDiv.innerText || ''

      if (subParagraphMarkerRegex.test(textContent)) {
        flushSubParagraph() // 遇到了新序号，处理掉之前收集的内容
      }
      currentSubParagraphLines.push(trimmedLine)
    }

    flushSubParagraph() // 处理循环结束后剩余的最后一部分内容

    return resultHtml
  }

  /**
   * 主函数：将容器内容按精确的标题规则结构化为模块
   * @param {HTMLElement} container - 需要处理的容器元素
   */
  function structureIntoModules(container) {
    // Point 1: 严格按照提供的列表定义所有可能的标题
    const allTitles = ['【接続】', '【意味】', '【例文】', '【説明】', '【基本例文】', '【練習】', '【答え】', '［使う時］', '［例文］', '［説明］']

    // 动态生成一个精确匹配的正则表达式
    // 首先对每个标题进行转义，以防特殊字符
    const escapedTitles = allTitles.map((t) => t.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'))
    const titlesRegex = new RegExp(`^\\s*(${escapedTitles.join('|')})\\s*$`)

    // 使用更安全的方式遍历和重建DOM
    const nodes = Array.from(container.childNodes)
    const newContainer = document.createDocumentFragment()
    let currentModule = null

    const createModule = (titleText) => {
      const section = document.createElement('section')
      section.className = 'content-module'

      // Point 2: 移除括号，统一设置标题样式
      const cleanTitle = titleText.replace(/[【】［］]/g, '')
      const header = document.createElement('h2')
      header.textContent = cleanTitle
      section.appendChild(header)

      newContainer.appendChild(section)
      return section
    }

    // 遍历所有节点，进行分组
    for (const node of nodes) {
      // 我们只关心顶级文本节点或简单包裹的元素节点作为标题
      const nodeText = (node.textContent || '').trim()

      if (node.nodeType === Node.TEXT_NODE && titlesRegex.test(nodeText)) {
        currentModule = createModule(nodeText)
      } else if (node.nodeType === Node.ELEMENT_NODE && titlesRegex.test(nodeText)) {
        // 检查被<b>, <span>等包裹的标题
        currentModule = createModule(nodeText)
      } else {
        // 如果不是标题，则属于前一个模块
        if (!currentModule) {
          // 处理文档开头、第一个标题出现前的零散内容
          currentModule = createModule('') // 创建一个没有标题的模块
          currentModule.querySelector('h2').style.display = 'none' // 隐藏空的h2
        }
        // 将非标题节点附加到当前模块的临时容器中，等待后续处理
        // 使用 cloneNode(true) 避免节点移动问题
        currentModule.appendChild(node.cloneNode(true))
      }
    }

    // 清空原始容器
    container.innerHTML = ''

    // 对每个构建好的模块进行内部处理
    Array.from(newContainer.childNodes).forEach((moduleSection) => {
      const h2 = moduleSection.querySelector('h2')
      const titleHtml = h2 ? h2.outerHTML : ''

      // 提取除h2外的内容
      if (h2) h2.remove()
      const contentHtml = moduleSection.innerHTML

      // 使用我们的核心函数处理模块内容
      const processedContent = processModuleContent(contentHtml)

      // 重新组合模块并放回容器
      moduleSection.innerHTML = titleHtml + `<div class="module-body">${processedContent}</div>`
      container.appendChild(moduleSection)
    })
  }

  // --- 主执行流程 ---
  function main() {
    // 阶段 1: 立即移除全局元素
    removeGlobalElements()
    // 阶段 2: 当 DOM 内容加载完成后处理主要内容
    const processMainContent = () => {
      const articleBodies = document.querySelectorAll('.article-body-inner')
      // 先快速处理内容
      articleBodies.forEach((body) => {
        cleanupContent(body)
        processRubyConversion(body)
        structureIntoModules(body)
        // 显示处理完成的内容
        body.style.opacity = 1
      })
      optimizeSidebar()
    }

    // 使用 MutationObserver 确保内容加载
    if (document.querySelector('.article-body-inner')) {
      processMainContent()
    } else {
      const observer = new MutationObserver((mutations, obs) => {
        if (document.querySelector('.article-body-inner')) {
          obs.disconnect()
          processMainContent()
        }
      })
      observer.observe(document, {
        childList: true,
        subtree: true,
      })
    }
  }

  // --- 启动脚本 ---
  if (document.readyState === 'complete') {
    main()
    createToggle()
  } else {
    document.addEventListener('DOMContentLoaded', () => {
      main()
      createToggle()
    })
    window.addEventListener('load', createToggle)
  }
})()
