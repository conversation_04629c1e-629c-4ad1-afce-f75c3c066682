/* 基本布局 */
#container {
  width: 100%;
}
@media (min-width: 1024px) {
  #container {
    max-width: 1024px;
  }
}
#content {
  display: flex;
  position: relative;
  padding: 50px 0 !important;
}
#main {
  flex: 1;
  float: none !important;
  min-width: 290px;
}

/* 侧边栏 */
aside#sidebar {
  visibility: hidden;
  float: none !important;
  width: 350px !important;
  flex: 0 0 350px;
}
.plugin-categorize {
  position: fixed;
  height: 85vh;
  display: flex;
  flex-direction: column;
  padding: 0 !important;
  width: 350px !important;
}
.plugin-categorize .side {
  flex: 1;
  overflow-y: auto;
  max-height: unset;
}
.plugin-categorize .side > :not([hidden]) ~ :not([hidden]) {
  margin-top: 5px;
  margin-bottom: 0;
}

/* 文章内容 */
.article {
  padding: 20px 0 !important;
}
.article-pager {
  margin-bottom: 0 !important;
}
img.pict {
  margin: 0 !important;
}

/* 返回顶部按钮 */
.to-pagetop {
  position: fixed;
  bottom: 20px;
  right: 100px;
  z-index: 1000;
}

/* 禁用文本选择和鼠标事件 */
rt {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  pointer-events: none;
}

/* 隐藏不必要的元素 */
header#blog-header,
footer#blog-footer,
.ldb_menu,
.article-social-btn,
.adsbygoogle,
#ldblog_related_articles_01d4ecf1,
#ad2 {
  display: none !important;
}

/* 覆盖清除浮动样式 */
.article-body-inner:after,
.article-meta:after,
#container:after,
#content:after,
article:after,
section:after,
.cf:after {
  content: none !important;
  display: none !important;
  height: auto !important;
  visibility: visible !important;
}
