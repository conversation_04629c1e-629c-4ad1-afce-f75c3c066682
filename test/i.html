<span style="font-size: 125%;"><img src="https://livedoor.blogimg.jp/edewakaru/imgs/3/7/379c060e.jpg" alt="blog頭が切れる"
    class="pict" width="304" height="431"><br>【】</span>
<div id="content">

  <div id="main"></div>

  <aside id="sidebar" class="column" style="visibility: visible;">
  </aside>

  <p class="to-pagetop"> </p>

</div>


content 是 flex 布局
to-pagetop 是 fixed 布局, 但却参与了flex布局的排列，有to-pagetop在 content 分成了3列
想让 main 和 sidebar 在左右显示，如何忽略 to-pagetop 的影响？



<aside id="sidebar">
  <div class="plugin-categorize sidewrapper">
    <div class="sidetitlebody">
      <div class="sidetitle">カテゴリー</div>
    </div>
    <div class="sidetop"></div>
    <div class="side">
      <div class="sidebody category-parent"><a href="https://www.edewakaru.com/archives/cat_179055.html">文法リスト (1)</a>
      </div>
    </div>
    <div class="sidebottom"></div>
  </div>
</aside>

.plugin-categorize {
position: fixed;
height: 80%;
padding: 0 !important;
}
.plugin-categorize .side {
overflow-y: auto;
max-height: 100%;
}


这样会导致 side 底部有些空白，当内部内容过长出现滚动条时，并不能完整看到最下面内容
